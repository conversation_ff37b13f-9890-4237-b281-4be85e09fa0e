namespace MaoYouJi
{
  /// <summary>
  /// 公会权限检查系统
  /// </summary>
  public static class GuildPermissionSystem
  {
    /// <summary>
    /// 检查用户是否有权限执行指定操作
    /// </summary>
    public static LogicRet CheckPermission(this GuildInfo guildInfo, long userId, GuildRole requiredRole)
    {
      if (guildInfo == null)
      {
        return LogicRet.Failed("公会不存在");
      }

      if (!guildInfo.memberInfos.TryGetValue(userId, out GuildMemberInfo memberInfo))
      {
        return LogicRet.Failed("您不是该公会成员");
      }

      if (memberInfo.role < requiredRole)
      {
        return LogicRet.Failed($"权限不足，需要{GetRoleDescription(requiredRole)}及以上权限");
      }

      return LogicRet.Success;
    }

    /// <summary>
    /// 检查是否可以邀请成员（管理员及以上）
    /// </summary>
    public static LogicRet CheckInvitePermission(this GuildInfo guildInfo, long userId)
    {
      return CheckPermission(guildInfo, userId, GuildRole.Manager);
    }

    /// <summary>
    /// 检查是否可以踢出成员（管理员及以上，且不能踢出同级或更高级别）
    /// </summary>
    public static LogicRet CheckKickPermission(this GuildInfo guildInfo, long operatorId, long targetId)
    {
      var permissionCheck = CheckPermission(guildInfo, operatorId, GuildRole.Manager);
      if (!permissionCheck.IsSuccess)
      {
        return permissionCheck;
      }

      if (!guildInfo.memberInfos.TryGetValue(operatorId, out GuildMemberInfo operatorInfo))
      {
        return LogicRet.Failed("操作者信息不存在");
      }

      if (!guildInfo.memberInfos.TryGetValue(targetId, out GuildMemberInfo targetInfo))
      {
        return LogicRet.Failed("目标用户不是公会成员");
      }

      // 不能踢出自己
      if (operatorId == targetId)
      {
        return LogicRet.Failed("不能踢出自己");
      }

      // 不能踢出同级或更高级别的成员
      if (targetInfo.role >= operatorInfo.role)
      {
        return LogicRet.Failed("不能踢出同级或更高级别的成员");
      }

      return LogicRet.Success;
    }

    /// <summary>
    /// 检查是否可以变更成员角色（副会长及以上，且只能管理下级）
    /// </summary>
    public static LogicRet CheckChangeRolePermission(this GuildInfo guildInfo, long operatorId, long targetId, GuildRole newRole)
    {
      var permissionCheck = CheckPermission(guildInfo, operatorId, GuildRole.VicePresident);
      if (!permissionCheck.IsSuccess)
      {
        return permissionCheck;
      }

      if (!guildInfo.memberInfos.TryGetValue(operatorId, out GuildMemberInfo operatorInfo))
      {
        return LogicRet.Failed("操作者信息不存在");
      }

      if (!guildInfo.memberInfos.TryGetValue(targetId, out GuildMemberInfo targetInfo))
      {
        return LogicRet.Failed("目标用户不是公会成员");
      }

      // 不能变更自己的角色
      if (operatorId == targetId)
      {
        return LogicRet.Failed("不能变更自己的角色");
      }

      // 只有会长可以任命副会长
      if (newRole == GuildRole.President)
      {
        return LogicRet.Failed("不能将成员提升为会长");
      }

      if (newRole == GuildRole.VicePresident && operatorInfo.role != GuildRole.President)
      {
        return LogicRet.Failed("只有会长可以任命副会长");
      }

      // 不能管理同级或更高级别的成员
      if (targetInfo.role >= operatorInfo.role)
      {
        return LogicRet.Failed("不能管理同级或更高级别的成员");
      }

      // 不能将成员提升到与自己同级或更高级别（除了会长）
      if (newRole >= operatorInfo.role && operatorInfo.role != GuildRole.President)
      {
        return LogicRet.Failed("不能将成员提升到与自己同级或更高级别");
      }

      return LogicRet.Success;
    }

    /// <summary>
    /// 检查是否可以编辑公会信息（副会长及以上）
    /// </summary>
    public static LogicRet CheckEditGuildPermission(this GuildInfo guildInfo, long userId)
    {
      return CheckPermission(guildInfo, userId, GuildRole.VicePresident);
    }

    /// <summary>
    /// 检查是否可以解散公会（只有会长）
    /// </summary>
    public static LogicRet CheckDisbandPermission(this GuildInfo guildInfo, long userId)
    {
      return CheckPermission(guildInfo, userId, GuildRole.President);
    }

    /// <summary>
    /// 检查是否可以转让会长（只有会长，且目标必须是副会长）
    /// </summary>
    public static LogicRet CheckTransferLeaderPermission(this GuildInfo guildInfo, long operatorId, long targetId)
    {
      var permissionCheck = CheckPermission(guildInfo, operatorId, GuildRole.President);
      if (!permissionCheck.IsSuccess)
      {
        return permissionCheck;
      }

      if (!guildInfo.memberInfos.TryGetValue(targetId, out GuildMemberInfo targetInfo))
      {
        return LogicRet.Failed("目标用户不是公会成员");
      }

      if (targetInfo.role != GuildRole.VicePresident)
      {
        return LogicRet.Failed("只能将会长职位转让给副会长");
      }

      if (operatorId == targetId)
      {
        return LogicRet.Failed("不能转让给自己");
      }

      return LogicRet.Success;
    }

    /// <summary>
    /// 检查是否可以管理联盟（副会长及以上）
    /// </summary>
    public static LogicRet CheckAlliancePermission(this GuildInfo guildInfo, long userId)
    {
      return CheckPermission(guildInfo, userId, GuildRole.VicePresident);
    }

    /// <summary>
    /// 检查是否可以解除联盟（只有会长）
    /// </summary>
    public static LogicRet CheckRemoveAlliancePermission(this GuildInfo guildInfo, long userId)
    {
      return CheckPermission(guildInfo, userId, GuildRole.President);
    }

    /// <summary>
    /// 获取角色描述
    /// </summary>
    public static string GetRoleDescription(this GuildRole role)
    {
      return role switch
      {
        GuildRole.Member => "会员",
        GuildRole.Manager => "管理员",
        GuildRole.VicePresident => "副会长",
        GuildRole.President => "会长",
        _ => "未知角色"
      };
    }

    /// <summary>
    /// 检查角色层级关系
    /// </summary>
    public static bool IsHigherRole(this GuildRole role1, GuildRole role2)
    {
      return (int)role1 > (int)role2;
    }

    /// <summary>
    /// 检查是否可以执行成员管理操作
    /// </summary>
    public static LogicRet CheckMemberManagementPermission(this GuildInfo guildInfo, long operatorId, long targetId, string operation)
    {
      if (!guildInfo.memberInfos.TryGetValue(operatorId, out GuildMemberInfo operatorInfo))
      {
        return LogicRet.Failed("您不是公会成员");
      }

      if (!guildInfo.memberInfos.TryGetValue(targetId, out GuildMemberInfo targetInfo))
      {
        return LogicRet.Failed("目标用户不是公会成员");
      }

      if (operatorId == targetId)
      {
        return LogicRet.Failed($"不能对自己执行{operation}操作");
      }

      if (targetInfo.role >= operatorInfo.role)
      {
        return LogicRet.Failed($"不能对同级或更高级别的成员执行{operation}操作");
      }

      return LogicRet.Success;
    }
  }
}
